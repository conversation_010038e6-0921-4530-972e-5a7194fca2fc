"""
测试Serena MCP工具是否正常工作的简单脚本
"""

def main():
    print("Hello from Serena MCP test!")
    
    # 这是一个简单的函数，可以被Serena分析
    def add(a, b):
        """将两个数字相加"""
        return a + b
    
    result = add(5, 7)
    print(f"5 + 7 = {result}")
    
    # 创建一个简单的类，可以被Serena分析
    class Person:
        """表示一个人的简单类"""
        
        def __init__(self, name, age):
            self.name = name
            self.age = age
            
        def greet(self):
            """返回问候语"""
            return f"你好，我是{self.name}，今年{self.age}岁。"
    
    # 创建Person实例并调用方法
    person = Person("张三", 30)
    greeting = person.greet()
    print(greeting)
    
    print("Serena MCP测试完成！")

if __name__ == "__main__":
    main()
